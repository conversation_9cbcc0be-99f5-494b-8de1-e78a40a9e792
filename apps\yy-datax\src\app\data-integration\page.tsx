'use client';

import { useState } from 'react';
import Link from 'next/link';

interface DataSource {
  id: string;
  name: string;
  type: 'http' | 'database' | 'ftp' | 'kafka' | 'mq' | 'file';
  status: 'connected' | 'disconnected' | 'error';
  config: any;
}

interface DataIntegration {
  id: string;
  name: string;
  source: DataSource;
  target: DataSource;
  mode: 'sync' | 'async';
  frequency: 'realtime' | 'hourly' | 'daily' | 'weekly';
  dataType: 'full' | 'incremental';
  status: 'active' | 'paused' | 'error';
  lastSync: string;
  nextSync: string;
}

export default function DataIntegrationPage() {
  const [activeTab, setActiveTab] = useState<'integrations' | 'sources' | 'new'>('integrations');
  const [selectedIntegration, setSelectedIntegration] = useState<string | null>(null);

  // 示例数据
  const integrations: DataIntegration[] = [
    {
      id: '1',
      name: 'HIS患者信息同步',
      source: { id: 's1', name: 'HIS系统', type: 'database', status: 'connected', config: {} },
      target: { id: 't1', name: '数据中心', type: 'http', status: 'connected', config: {} },
      mode: 'sync',
      frequency: 'realtime',
      dataType: 'incremental',
      status: 'active',
      lastSync: '2分钟前',
      nextSync: '实时'
    },
    {
      id: '2',
      name: '检验报告推送',
      source: { id: 's2', name: 'LIS系统', type: 'database', status: 'connected', config: {} },
      target: { id: 't2', name: 'HIS系统', type: 'kafka', status: 'connected', config: {} },
      mode: 'async',
      frequency: 'hourly',
      dataType: 'incremental',
      status: 'active',
      lastSync: '30分钟前',
      nextSync: '30分钟后'
    },
    {
      id: '3',
      name: '影像文件传输',
      source: { id: 's3', name: 'PACS系统', type: 'ftp', status: 'connected', config: {} },
      target: { id: 't3', name: '云存储', type: 'ftp', status: 'error', config: {} },
      mode: 'async',
      frequency: 'daily',
      dataType: 'full',
      status: 'error',
      lastSync: '2小时前',
      nextSync: '暂停'
    }
  ];

  const dataSources: DataSource[] = [
    { id: 's1', name: 'HIS系统', type: 'database', status: 'connected', config: {} },
    { id: 's2', name: 'LIS系统', type: 'database', status: 'connected', config: {} },
    { id: 's3', name: 'PACS系统', type: 'ftp', status: 'connected', config: {} },
    { id: 's4', name: '药房系统', type: 'mq', status: 'disconnected', config: {} },
    { id: 't1', name: '数据中心', type: 'http', status: 'connected', config: {} },
    { id: 't2', name: 'Kafka集群', type: 'kafka', status: 'connected', config: {} },
    { id: 't3', name: '云存储', type: 'ftp', status: 'error', config: {} }
  ];

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: 'status-success',
      connected: 'status-success',
      paused: 'status-warning',
      disconnected: 'status-warning',
      error: 'status-error'
    };
    return statusMap[status as keyof typeof statusMap] || 'status-secondary';
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      active: '运行中',
      connected: '已连接',
      paused: '已暂停',
      disconnected: '未连接',
      error: '异常'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      http: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
        </svg>
      ),
      database: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
        </svg>
      ),
      ftp: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
        </svg>
      ),
      kafka: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      ),
      mq: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      file: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    };
    return icons[type as keyof typeof icons] || icons.file;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100">
      {/* 导航栏 */}
      <nav className="bg-white shadow-soft border-b border-secondary-200">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Link href="/">
                  <h1 className="text-2xl font-bold text-primary-600">YY DataX</h1>
                </Link>
              </div>
              <div className="hidden md:block ml-10">
                <div className="flex items-baseline space-x-4">
                  <Link href="/" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    首页
                  </Link>
                  <Link href="/data-map" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    数据地图
                  </Link>
                  <Link href="/system-register" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    系统注册
                  </Link>
                  <Link href="/data-subscription" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    数据订阅
                  </Link>
                  <Link href="/data-integration" className="text-primary-600 hover:text-primary-800 px-3 py-2 rounded-md text-sm font-medium">
                    数据对接
                  </Link>
                  <Link href="/monitoring" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    监控
                  </Link>
                  <Link href="/settings" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    设置
                  </Link>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-secondary-600">欢迎，管理员</span>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-full mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-900 mb-2">数据对接管理</h1>
          <p className="text-lg text-secondary-600">配置和管理医院各系统间的数据传输对接</p>
        </div>

        {/* 标签页导航 */}
        <div className="mb-6">
          <div className="border-b border-secondary-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('integrations')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'integrations'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                对接任务
              </button>
              <button
                onClick={() => setActiveTab('sources')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'sources'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                数据源管理
              </button>
              <button
                onClick={() => setActiveTab('new')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'new'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                新建对接
              </button>
            </nav>
          </div>
        </div>

        {/* 对接任务列表 */}
        {activeTab === 'integrations' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-secondary-900">数据对接任务</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-secondary-200">
                <thead className="table-header">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      任务名称
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      数据源
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      目标系统
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      同步方式
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      最后同步
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-secondary-200">
                  {integrations.map((integration) => (
                    <tr key={integration.id} className="table-row">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-secondary-900">{integration.name}</div>
                        <div className="text-sm text-secondary-500">
                          {integration.frequency === 'realtime' ? '实时' :
                           integration.frequency === 'hourly' ? '每小时' :
                           integration.frequency === 'daily' ? '每日' : '每周'} ·
                          {integration.dataType === 'full' ? '全量' : '增量'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-primary-600 mr-2">
                            {getTypeIcon(integration.source.type)}
                          </div>
                          <div className="text-sm text-secondary-900">{integration.source.name}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-primary-600 mr-2">
                            {getTypeIcon(integration.target.type)}
                          </div>
                          <div className="text-sm text-secondary-900">{integration.target.name}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                        {integration.mode === 'sync' ? '同步' : '异步'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`status-badge ${getStatusBadge(integration.status)}`}>
                          {getStatusText(integration.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                        {integration.lastSync}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button className="text-primary-600 hover:text-primary-900 mr-3">编辑</button>
                        <button className="text-warning-600 hover:text-warning-900 mr-3">
                          {integration.status === 'active' ? '暂停' : '启动'}
                        </button>
                        <button className="text-error-600 hover:text-error-900">删除</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* 数据源管理 */}
        {activeTab === 'sources' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-secondary-900">数据源列表</h3>
              </div>
              <div className="space-y-4">
                {dataSources.map((source) => (
                  <div key={source.id} className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg">
                    <div className="flex items-center">
                      <div className="text-primary-600 mr-3">
                        {getTypeIcon(source.type)}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-secondary-900">{source.name}</div>
                        <div className="text-sm text-secondary-500 capitalize">{source.type}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`status-badge ${getStatusBadge(source.status)}`}>
                        {getStatusText(source.status)}
                      </span>
                      <button className="text-primary-600 hover:text-primary-900">配置</button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-secondary-900">添加数据源</h3>
              </div>
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">数据源名称</label>
                  <input type="text" className="input-field" placeholder="请输入数据源名称" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">数据源类型</label>
                  <select className="input-field">
                    <option value="">请选择数据源类型</option>
                    <option value="database">数据库</option>
                    <option value="http">HTTP API</option>
                    <option value="ftp">FTP服务器</option>
                    <option value="kafka">Kafka</option>
                    <option value="mq">消息队列</option>
                    <option value="file">文件系统</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">连接地址</label>
                  <input type="text" className="input-field" placeholder="请输入连接地址" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">用户名</label>
                    <input type="text" className="input-field" placeholder="用户名" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">密码</label>
                    <input type="password" className="input-field" placeholder="密码" />
                  </div>
                </div>
                <div className="flex space-x-3">
                  <button type="button" className="btn-secondary">测试连接</button>
                  <button type="submit" className="btn-primary">添加数据源</button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* 新建对接 */}
        {activeTab === 'new' && (
          <div className="card max-w-4xl">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-secondary-900">新建数据对接任务</h3>
            </div>
            <form className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">任务名称</label>
                <input type="text" className="input-field" placeholder="请输入对接任务名称" />
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-secondary-900">数据源配置</h4>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">选择数据源</label>
                    <select className="input-field">
                      <option value="">请选择数据源</option>
                      {dataSources.filter(s => s.status === 'connected').map(source => (
                        <option key={source.id} value={source.id}>{source.name}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">数据表/接口</label>
                    <input type="text" className="input-field" placeholder="请输入数据表名或接口路径" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">过滤条件</label>
                    <textarea className="input-field" rows={3} placeholder="请输入SQL WHERE条件或过滤规则"></textarea>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-md font-medium text-secondary-900">目标配置</h4>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">选择目标系统</label>
                    <select className="input-field">
                      <option value="">请选择目标系统</option>
                      {dataSources.filter(s => s.status === 'connected').map(source => (
                        <option key={source.id} value={source.id}>{source.name}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">目标表/接口</label>
                    <input type="text" className="input-field" placeholder="请输入目标表名或接口路径" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">字段映射</label>
                    <textarea className="input-field" rows={3} placeholder="请输入字段映射规则（JSON格式）"></textarea>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">同步方式</label>
                  <select className="input-field">
                    <option value="sync">同步</option>
                    <option value="async">异步</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">同步频率</label>
                  <select className="input-field">
                    <option value="realtime">实时</option>
                    <option value="hourly">每小时</option>
                    <option value="daily">每日</option>
                    <option value="weekly">每周</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">数据类型</label>
                  <select className="input-field">
                    <option value="incremental">增量</option>
                    <option value="full">全量</option>
                  </select>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-md font-medium text-secondary-900">高级配置</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">批处理大小</label>
                    <input type="number" className="input-field" placeholder="1000" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">超时时间（秒）</label>
                    <input type="number" className="input-field" placeholder="300" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">重试次数</label>
                    <input type="number" className="input-field" placeholder="3" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">重试间隔（秒）</label>
                    <input type="number" className="input-field" placeholder="60" />
                  </div>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" id="autoStart" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" />
                  <label htmlFor="autoStart" className="ml-2 block text-sm text-secondary-900">
                    创建后自动启动
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button type="button" className="btn-secondary">取消</button>
                <button type="button" className="btn-secondary">保存草稿</button>
                <button type="submit" className="btn-primary">创建对接任务</button>
              </div>
            </form>
          </div>
        )}
      </main>
    </div>
  );
}
