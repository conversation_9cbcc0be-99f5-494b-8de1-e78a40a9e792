'use client';

import { useState } from 'react';
import Link from 'next/link';

interface SubscriptionApplication {
  id: string;
  dataResourceName: string;
  provider: string;
  applicant: string;
  applicantSystem: string;
  purpose: string;
  status: 'pending' | 'approved' | 'rejected' | 'active';
  appliedAt: string;
  approvedAt?: string;
  accessKey?: string;
}

const mockApplications: SubscriptionApplication[] = [
  {
    id: '1',
    dataResourceName: '患者基本信息',
    provider: 'HIS系统',
    applicant: '检验科',
    applicantSystem: 'LIS系统',
    purpose: '检验报告关联患者信息',
    status: 'active',
    appliedAt: '2024-01-10 09:30:00',
    approvedAt: '2024-01-10 14:20:00',
    accessKey: 'ak_lis_patient_info_2024'
  },
  {
    id: '2',
    dataResourceName: '检验报告数据',
    provider: '检验科LIS',
    applicant: '影像科',
    applicantSystem: 'PACS系统',
    purpose: '影像诊断参考检验结果',
    status: 'approved',
    appliedAt: '2024-01-12 11:15:00',
    approvedAt: '2024-01-12 16:45:00',
    accessKey: 'ak_pacs_lab_reports_2024'
  },
  {
    id: '3',
    dataResourceName: '药品库存信息',
    provider: '药房系统',
    applicant: '门诊部',
    applicantSystem: 'HIS系统',
    purpose: '门诊开药库存检查',
    status: 'pending',
    appliedAt: '2024-01-14 15:20:00'
  },
  {
    id: '4',
    dataResourceName: '影像检查资料',
    provider: '影像科PACS',
    applicant: '外科',
    applicantSystem: 'EMR系统',
    purpose: '手术规划参考影像资料',
    status: 'rejected',
    appliedAt: '2024-01-13 10:00:00'
  }
];

interface NewApplicationForm {
  dataResourceId: string;
  dataResourceName: string;
  applicantDepartment: string;
  applicantSystem: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
  purpose: string;
  usageDescription: string;
  dataRetentionPeriod: string;
  securityMeasures: string;
}

export default function DataSubscription() {
  const [activeTab, setActiveTab] = useState<'applications' | 'new'>('applications');
  const [applications] = useState(mockApplications);

  const [newApplication, setNewApplication] = useState<NewApplicationForm>({
    dataResourceId: '',
    dataResourceName: '',
    applicantDepartment: '',
    applicantSystem: '',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    purpose: '',
    usageDescription: '',
    dataRetentionPeriod: '1年',
    securityMeasures: ''
  });

  const statusLabels = {
    pending: '待审核',
    approved: '已批准',
    rejected: '已拒绝',
    active: '使用中'
  };

  const statusColors = {
    pending: 'status-warning',
    approved: 'status-info',
    rejected: 'status-error',
    active: 'status-success'
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewApplication(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmitApplication = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('提交订阅申请:', newApplication);
    alert('数据订阅申请已提交，请等待审核！');
    // 重置表单
    setNewApplication({
      dataResourceId: '',
      dataResourceName: '',
      applicantDepartment: '',
      applicantSystem: '',
      contactPerson: '',
      contactPhone: '',
      contactEmail: '',
      purpose: '',
      usageDescription: '',
      dataRetentionPeriod: '1年',
      securityMeasures: ''
    });
    setActiveTab('applications');
  };

  const handleApprove = (id: string) => {
    alert(`批准订阅申请: ${id}`);
  };

  const handleReject = (id: string) => {
    alert(`拒绝订阅申请: ${id}`);
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-soft border-b border-secondary-200">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-primary-600">YY DataX</Link>
              <span className="ml-4 text-secondary-500">/</span>
              <span className="ml-4 text-secondary-700">数据订阅</span>
            </div>
            <Link href="/" className="text-secondary-600 hover:text-secondary-900">
              返回首页
            </Link>
          </div>
        </div>
      </nav>

      <div className="max-w-full mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-900">数据订阅管理</h1>
          <p className="mt-2 text-lg text-secondary-600">
            申请和管理数据资源订阅
          </p>
        </div>

        {/* 标签页导航 */}
        <div className="mb-8">
          <div className="border-b border-secondary-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('applications')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'applications'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                我的申请
              </button>
              <button
                onClick={() => setActiveTab('new')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'new'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                新建申请
              </button>
            </nav>
          </div>
        </div>

        {/* 我的申请列表 */}
        {activeTab === 'applications' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-secondary-900">我的申请列表</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-secondary-200">
                <thead className="table-header">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      数据资源
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      提供方
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      申请部门/系统
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      申请时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-secondary-200">
                  {applications.map((app) => (
                    <tr key={app.id} className="table-row">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-secondary-900">{app.dataResourceName}</div>
                        <div className="text-sm text-secondary-500 truncate max-w-xs" title={app.purpose}>
                          {app.purpose}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                        {app.provider}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">{app.applicant}</div>
                        <div className="text-sm text-secondary-500">{app.applicantSystem}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">{app.appliedAt}</div>
                        {app.approvedAt && (
                          <div className="text-sm text-secondary-500">批准: {app.approvedAt}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`status-badge ${statusColors[app.status]}`}>
                          {statusLabels[app.status]}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button className="text-primary-600 hover:text-primary-900">详情</button>
                          {app.status === 'pending' && (
                            <>
                              <button
                                onClick={() => handleApprove(app.id)}
                                className="text-success-600 hover:text-success-900"
                              >
                                批准
                              </button>
                              <button
                                onClick={() => handleReject(app.id)}
                                className="text-error-600 hover:text-error-900"
                              >
                                拒绝
                              </button>
                            </>
                          )}
                          {app.accessKey && (
                            <button
                              className="text-warning-600 hover:text-warning-900"
                              onClick={() => navigator.clipboard.writeText(app.accessKey!)}
                              title="复制访问密钥"
                            >
                              密钥
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 访问密钥详情弹窗或展开区域 */}
            <div className="mt-6 space-y-4">
              {applications.filter(app => app.accessKey).map((app) => (
                <div key={`key-${app.id}`} className="bg-success-50 border border-success-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-6 6c-3 0-5.5-1.5-5.5-4a3.5 3.5 0 00-3.5-3.5M9 10a2 2 0 012-2m0 0V6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H9a2 2 0 01-2-2z" />
                      </svg>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-success-800">{app.dataResourceName} - 访问密钥</h4>
                        <p className="text-sm text-success-700 font-mono">{app.accessKey}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => navigator.clipboard.writeText(app.accessKey!)}
                      className="btn-secondary text-xs"
                    >
                      复制
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 新建申请表单 */}
        {activeTab === 'new' && (
          <form onSubmit={handleSubmitApplication} className="card">
            <h3 className="text-lg font-semibold text-secondary-900 mb-6">新建数据订阅申请</h3>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="label">数据资源名称 *</label>
                  <input
                    type="text"
                    name="dataResourceName"
                    value={newApplication.dataResourceName}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="选择或输入数据资源名称"
                    required
                  />
                </div>
                <div>
                  <label className="label">申请部门 *</label>
                  <select
                    name="applicantDepartment"
                    value={newApplication.applicantDepartment}
                    onChange={handleInputChange}
                    className="input-field"
                    required
                  >
                    <option value="">请选择部门</option>
                    <option value="门诊部">门诊部</option>
                    <option value="住院部">住院部</option>
                    <option value="检验科">检验科</option>
                    <option value="影像科">影像科</option>
                    <option value="药剂科">药剂科</option>
                    <option value="信息科">信息科</option>
                    <option value="其他">其他</option>
                  </select>
                </div>
                <div>
                  <label className="label">申请系统 *</label>
                  <input
                    type="text"
                    name="applicantSystem"
                    value={newApplication.applicantSystem}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="如：LIS系统、PACS系统等"
                    required
                  />
                </div>
                <div>
                  <label className="label">联系人 *</label>
                  <input
                    type="text"
                    name="contactPerson"
                    value={newApplication.contactPerson}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="负责人姓名"
                    required
                  />
                </div>
                <div>
                  <label className="label">联系电话 *</label>
                  <input
                    type="tel"
                    name="contactPhone"
                    value={newApplication.contactPhone}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="联系电话"
                    required
                  />
                </div>
                <div>
                  <label className="label">邮箱地址 *</label>
                  <input
                    type="email"
                    name="contactEmail"
                    value={newApplication.contactEmail}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="邮箱地址"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="label">申请用途 *</label>
                <textarea
                  name="purpose"
                  value={newApplication.purpose}
                  onChange={handleInputChange}
                  className="input-field"
                  rows={3}
                  placeholder="详细说明数据使用目的和业务需求"
                  required
                />
              </div>

              <div>
                <label className="label">使用方式描述 *</label>
                <textarea
                  name="usageDescription"
                  value={newApplication.usageDescription}
                  onChange={handleInputChange}
                  className="input-field"
                  rows={3}
                  placeholder="描述如何使用这些数据，包括访问频率、处理方式等"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="label">数据保留期限 *</label>
                  <select
                    name="dataRetentionPeriod"
                    value={newApplication.dataRetentionPeriod}
                    onChange={handleInputChange}
                    className="input-field"
                    required
                  >
                    <option value="3个月">3个月</option>
                    <option value="6个月">6个月</option>
                    <option value="1年">1年</option>
                    <option value="2年">2年</option>
                    <option value="长期">长期</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="label">安全保障措施 *</label>
                <textarea
                  name="securityMeasures"
                  value={newApplication.securityMeasures}
                  onChange={handleInputChange}
                  className="input-field"
                  rows={3}
                  placeholder="描述数据安全保护措施，如加密、访问控制、审计日志等"
                  required
                />
              </div>
            </div>

            <div className="flex justify-end mt-8 pt-6 border-t border-secondary-200">
              <button
                type="button"
                onClick={() => setActiveTab('applications')}
                className="btn-secondary mr-3"
              >
                取消
              </button>
              <button
                type="submit"
                className="btn-primary"
              >
                提交申请
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
