'use client';

import { useState } from 'react';
import Link from 'next/link';

interface MonitoringData {
  id: string;
  taskName: string;
  source: string;
  target: string;
  status: 'success' | 'warning' | 'error' | 'running';
  lastSync: string;
  duration: string;
  recordsProcessed: number;
  errorCount: number;
  message?: string;
}

interface SystemMetrics {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
}

interface DataFlowNode {
  id: string;
  name: string;
  type: 'hospital' | 'system';
  category: string;
  status: 'active' | 'warning' | 'error';
  connections: string[];
}

interface DataFlow {
  id: string;
  source: string;
  target: string;
  dataType: string;
  status: 'active' | 'warning' | 'error';
  throughput: number;
  lastUpdate: string;
}

export default function MonitoringPage() {
  const [activeTab, setActiveTab] = useState<'dataflow' | 'realtime' | 'history' | 'alerts' | 'performance'>('dataflow');
  const [timeRange, setTimeRange] = useState<'1h' | '6h' | '24h' | '7d'>('1h');
  const [viewDimension, setViewDimension] = useState<'hospital' | 'system'>('hospital');

  // 示例数据
  const realtimeData: MonitoringData[] = [
    {
      id: '1',
      taskName: 'HIS患者信息同步',
      source: 'HIS系统',
      target: '数据中心',
      status: 'success',
      lastSync: '刚刚',
      duration: '0.5s',
      recordsProcessed: 156,
      errorCount: 0
    },
    {
      id: '2',
      taskName: '检验报告推送',
      source: 'LIS系统',
      target: 'HIS系统',
      status: 'running',
      lastSync: '进行中',
      duration: '2.3s',
      recordsProcessed: 89,
      errorCount: 0
    },
    {
      id: '3',
      taskName: '影像文件传输',
      source: 'PACS系统',
      target: '云存储',
      status: 'error',
      lastSync: '5分钟前',
      duration: '超时',
      recordsProcessed: 0,
      errorCount: 1,
      message: '连接超时：无法连接到目标FTP服务器'
    },
    {
      id: '4',
      taskName: '药房库存同步',
      source: '药房系统',
      target: 'HIS系统',
      status: 'warning',
      lastSync: '10分钟前',
      duration: '15.2s',
      recordsProcessed: 234,
      errorCount: 3,
      message: '部分记录同步失败，已重试'
    }
  ];

  const systemMetrics: SystemMetrics = {
    cpu: 45,
    memory: 68,
    disk: 32,
    network: 78
  };

  // 数据流节点和连接
  const hospitalNodes: DataFlowNode[] = [
    { id: 'h1', name: '第一人民医院', type: 'hospital', category: '三甲医院', status: 'active', connections: ['s1', 's2', 's3'] },
    { id: 'h2', name: '中心医院', type: 'hospital', category: '三甲医院', status: 'warning', connections: ['s4', 's5'] },
    { id: 'h3', name: '社区医院', type: 'hospital', category: '社区医院', status: 'active', connections: ['s6'] }
  ];

  const systemNodes: DataFlowNode[] = [
    { id: 's1', name: 'HIS系统', type: 'system', category: '核心系统', status: 'active', connections: ['s2', 's3', 's4'] },
    { id: 's2', name: 'LIS系统', type: 'system', category: '检验系统', status: 'active', connections: ['s1'] },
    { id: 's3', name: 'PACS系统', type: 'system', category: '影像系统', status: 'error', connections: ['s1'] },
    { id: 's4', name: '药房系统', type: 'system', category: '药事系统', status: 'warning', connections: ['s1'] },
    { id: 's5', name: 'EMR系统', type: 'system', category: '电子病历', status: 'active', connections: ['s1'] },
    { id: 's6', name: '护理系统', type: 'system', category: '护理管理', status: 'active', connections: ['s1'] }
  ];

  const dataFlows: DataFlow[] = [
    { id: 'f1', source: 'HIS系统', target: '数据中心', dataType: '患者信息', status: 'active', throughput: 1200, lastUpdate: '2分钟前' },
    { id: 'f2', source: 'LIS系统', target: 'HIS系统', dataType: '检验报告', status: 'active', throughput: 800, lastUpdate: '5分钟前' },
    { id: 'f3', source: 'PACS系统', target: '云存储', dataType: '影像文件', status: 'error', throughput: 0, lastUpdate: '30分钟前' },
    { id: 'f4', source: '药房系统', target: 'HIS系统', dataType: '药品信息', status: 'warning', throughput: 300, lastUpdate: '10分钟前' }
  ];

  const alerts = [
    {
      id: '1',
      level: 'error',
      title: 'PACS系统连接失败',
      message: '影像文件传输任务连续失败超过5次',
      time: '5分钟前',
      resolved: false
    },
    {
      id: '2',
      level: 'warning',
      title: '数据同步延迟',
      message: '药房库存同步延迟超过预期时间',
      time: '15分钟前',
      resolved: false
    },
    {
      id: '3',
      level: 'info',
      title: '系统维护通知',
      message: '数据中心将于今晚22:00-23:00进行维护',
      time: '1小时前',
      resolved: true
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusMap = {
      success: 'status-success',
      running: 'status-primary',
      warning: 'status-warning',
      error: 'status-error'
    };
    return statusMap[status as keyof typeof statusMap] || 'status-secondary';
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      success: '成功',
      running: '运行中',
      warning: '警告',
      error: '错误'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getAlertIcon = (level: string) => {
    const icons = {
      error: (
        <svg className="w-5 h-5 text-error-500" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
        </svg>
      ),
      warning: (
        <svg className="w-5 h-5 text-warning-500" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      ),
      info: (
        <svg className="w-5 h-5 text-primary-500" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
        </svg>
      )
    };
    return icons[level as keyof typeof icons] || icons.info;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100">
      {/* 导航栏 */}
      <nav className="bg-white shadow-soft border-b border-secondary-200">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Link href="/">
                  <h1 className="text-2xl font-bold text-primary-600">YY DataX</h1>
                </Link>
              </div>
              <div className="hidden md:block ml-10">
                <div className="flex items-baseline space-x-4">
                  <Link href="/" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    首页
                  </Link>
                  <Link href="/data-map" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    数据地图
                  </Link>
                  <Link href="/system-register" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    系统注册
                  </Link>
                  <Link href="/data-subscription" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    数据订阅
                  </Link>
                  <Link href="/data-integration" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    数据对接
                  </Link>
                  <Link href="/monitoring" className="text-primary-600 hover:text-primary-800 px-3 py-2 rounded-md text-sm font-medium">
                    监控
                  </Link>
                  <Link href="/settings" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    设置
                  </Link>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-secondary-600">欢迎，管理员</span>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-full mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-900 mb-2">系统监控</h1>
          <p className="text-lg text-secondary-600">实时监控数据传输状态和系统性能</p>
        </div>

        {/* 系统指标概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">CPU使用率</p>
                <p className="text-2xl font-semibold text-secondary-900">{systemMetrics.cpu}%</p>
              </div>
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-secondary-200 rounded-full h-2">
                <div className="bg-primary-600 h-2 rounded-full" style={{ width: `${systemMetrics.cpu}%` }}></div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">内存使用率</p>
                <p className="text-2xl font-semibold text-secondary-900">{systemMetrics.memory}%</p>
              </div>
              <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                </svg>
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-secondary-200 rounded-full h-2">
                <div className="bg-success-600 h-2 rounded-full" style={{ width: `${systemMetrics.memory}%` }}></div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">磁盘使用率</p>
                <p className="text-2xl font-semibold text-secondary-900">{systemMetrics.disk}%</p>
              </div>
              <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-secondary-200 rounded-full h-2">
                <div className="bg-warning-600 h-2 rounded-full" style={{ width: `${systemMetrics.disk}%` }}></div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">网络使用率</p>
                <p className="text-2xl font-semibold text-secondary-900">{systemMetrics.network}%</p>
              </div>
              <div className="w-12 h-12 bg-error-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-error-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                </svg>
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-secondary-200 rounded-full h-2">
                <div className="bg-error-600 h-2 rounded-full" style={{ width: `${systemMetrics.network}%` }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="mb-6">
          <div className="border-b border-secondary-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('dataflow')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'dataflow'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                数据流向
              </button>
              <button
                onClick={() => setActiveTab('realtime')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'realtime'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                实时监控
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'history'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                历史记录
              </button>
              <button
                onClick={() => setActiveTab('alerts')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'alerts'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                告警中心
              </button>
              <button
                onClick={() => setActiveTab('performance')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'performance'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                性能分析
              </button>
            </nav>
          </div>
        </div>

        {/* 数据流向图 */}
        {activeTab === 'dataflow' && (
          <div className="space-y-6">
            {/* 维度切换 */}
            <div className="card">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-secondary-900">数据流向关系图</h3>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-secondary-600">查看维度:</span>
                  <div className="flex bg-secondary-100 rounded-lg p-1">
                    <button
                      onClick={() => setViewDimension('hospital')}
                      className={`px-3 py-1 text-sm rounded-md transition-colors ${
                        viewDimension === 'hospital'
                          ? 'bg-white text-primary-600 shadow-sm'
                          : 'text-secondary-600 hover:text-secondary-900'
                      }`}
                    >
                      医院单位
                    </button>
                    <button
                      onClick={() => setViewDimension('system')}
                      className={`px-3 py-1 text-sm rounded-md transition-colors ${
                        viewDimension === 'system'
                          ? 'bg-white text-primary-600 shadow-sm'
                          : 'text-secondary-600 hover:text-secondary-900'
                      }`}
                    >
                      系统维度
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* 数据流向可视化 */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 节点列表 */}
              <div className="card">
                <div className="card-header">
                  <h4 className="text-md font-semibold text-secondary-900">
                    {viewDimension === 'hospital' ? '医院单位' : '系统节点'}
                  </h4>
                </div>
                <div className="space-y-3">
                  {(viewDimension === 'hospital' ? hospitalNodes : systemNodes).map((node) => (
                    <div key={node.id} className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full mr-3 ${
                          node.status === 'active' ? 'bg-success-500' :
                          node.status === 'warning' ? 'bg-warning-500' : 'bg-error-500'
                        }`}></div>
                        <div>
                          <div className="text-sm font-medium text-secondary-900">{node.name}</div>
                          <div className="text-xs text-secondary-500">{node.category}</div>
                        </div>
                      </div>
                      <div className="text-xs text-secondary-500">
                        {node.connections.length} 连接
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 数据流状态 */}
              <div className="lg:col-span-2 card">
                <div className="card-header">
                  <h4 className="text-md font-semibold text-secondary-900">实时数据流</h4>
                </div>
                <div className="space-y-4">
                  {dataFlows.map((flow) => (
                    <div key={flow.id} className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className={`w-2 h-2 rounded-full ${
                          flow.status === 'active' ? 'bg-success-500 animate-pulse' :
                          flow.status === 'warning' ? 'bg-warning-500' : 'bg-error-500'
                        }`}></div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-secondary-900">{flow.source}</span>
                          <svg className="w-4 h-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                          </svg>
                          <span className="text-sm font-medium text-secondary-900">{flow.target}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-secondary-900">{flow.dataType}</div>
                        <div className="text-xs text-secondary-500">
                          {flow.throughput > 0 ? `${flow.throughput} 条/分钟` : '已停止'}
                        </div>
                        <div className="text-xs text-secondary-500">{flow.lastUpdate}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 流量统计 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="card text-center">
                <div className="text-2xl font-bold text-primary-600">
                  {dataFlows.filter(f => f.status === 'active').length}
                </div>
                <div className="text-sm text-secondary-600">活跃连接</div>
              </div>
              <div className="card text-center">
                <div className="text-2xl font-bold text-success-600">
                  {dataFlows.reduce((sum, f) => sum + f.throughput, 0)}
                </div>
                <div className="text-sm text-secondary-600">总吞吐量/分钟</div>
              </div>
              <div className="card text-center">
                <div className="text-2xl font-bold text-warning-600">
                  {dataFlows.filter(f => f.status === 'warning').length}
                </div>
                <div className="text-sm text-secondary-600">警告连接</div>
              </div>
              <div className="card text-center">
                <div className="text-2xl font-bold text-error-600">
                  {dataFlows.filter(f => f.status === 'error').length}
                </div>
                <div className="text-sm text-secondary-600">异常连接</div>
              </div>
            </div>
          </div>
        )}

        {/* 实时监控 */}
        {activeTab === 'realtime' && (
          <div className="space-y-6">
            <div className="card">
              <div className="card-header flex justify-between items-center">
                <h3 className="text-lg font-semibold text-secondary-900">实时数据传输状态</h3>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-secondary-600">实时更新</span>
                </div>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-secondary-200">
                  <thead className="table-header">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        任务名称
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        数据源
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        目标系统
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        处理记录数
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        耗时
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        错误数
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-secondary-200">
                    {realtimeData.map((item) => (
                      <tr key={item.id} className="table-row">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-secondary-900">{item.taskName}</div>
                          {item.message && (
                            <div className="text-sm text-error-600">{item.message}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                          {item.source}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                          {item.target}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`status-badge ${getStatusBadge(item.status)}`}>
                            {getStatusText(item.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          {item.recordsProcessed.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                          {item.duration}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                          {item.errorCount > 0 ? (
                            <span className="text-error-600 font-medium">{item.errorCount}</span>
                          ) : (
                            <span className="text-success-600">0</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button className="text-primary-600 hover:text-primary-900 mr-3">详情</button>
                          {item.status === 'error' && (
                            <button className="text-warning-600 hover:text-warning-900">重试</button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* 告警中心 */}
        {activeTab === 'alerts' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-secondary-900">系统告警</h3>
            </div>
            <div className="space-y-4">
              {alerts.map((alert) => (
                <div key={alert.id} className={`p-4 rounded-lg border-l-4 ${
                  alert.level === 'error' ? 'bg-error-50 border-error-400' :
                  alert.level === 'warning' ? 'bg-warning-50 border-warning-400' :
                  'bg-primary-50 border-primary-400'
                } ${alert.resolved ? 'opacity-60' : ''}`}>
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      {getAlertIcon(alert.level)}
                    </div>
                    <div className="ml-3 flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-secondary-900">{alert.title}</h4>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-secondary-500">{alert.time}</span>
                          {alert.resolved ? (
                            <span className="status-badge status-success">已解决</span>
                          ) : (
                            <span className="status-badge status-warning">待处理</span>
                          )}
                        </div>
                      </div>
                      <p className="mt-1 text-sm text-secondary-600">{alert.message}</p>
                      {!alert.resolved && (
                        <div className="mt-3 flex space-x-2">
                          <button className="btn-primary text-xs">处理</button>
                          <button className="btn-secondary text-xs">忽略</button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 历史记录 */}
        {activeTab === 'history' && (
          <div className="card">
            <div className="card-header flex justify-between items-center">
              <h3 className="text-lg font-semibold text-secondary-900">历史记录</h3>
              <div className="flex items-center space-x-4">
                <select
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value as any)}
                  className="input-field text-sm"
                >
                  <option value="1h">最近1小时</option>
                  <option value="6h">最近6小时</option>
                  <option value="24h">最近24小时</option>
                  <option value="7d">最近7天</option>
                </select>
                <button className="btn-secondary text-sm">导出</button>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-secondary-200">
                <thead className="table-header">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      任务名称
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      处理记录数
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      耗时
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      详情
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-secondary-200">
                  {realtimeData.map((item, index) => (
                    <tr key={`${item.id}-${index}`} className="table-row">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                        {new Date(Date.now() - index * 300000).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-secondary-900">
                        {item.taskName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`status-badge ${getStatusBadge(item.status)}`}>
                          {getStatusText(item.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                        {item.recordsProcessed.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                        {item.duration}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button className="text-primary-600 hover:text-primary-900">查看</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* 性能分析 */}
        {activeTab === 'performance' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-secondary-900">吞吐量统计</h3>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-secondary-50 rounded-lg">
                  <span className="text-sm font-medium text-secondary-700">每秒处理记录数</span>
                  <span className="text-lg font-semibold text-primary-600">1,234</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-secondary-50 rounded-lg">
                  <span className="text-sm font-medium text-secondary-700">平均响应时间</span>
                  <span className="text-lg font-semibold text-success-600">0.8s</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-secondary-50 rounded-lg">
                  <span className="text-sm font-medium text-secondary-700">成功率</span>
                  <span className="text-lg font-semibold text-success-600">99.2%</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-secondary-50 rounded-lg">
                  <span className="text-sm font-medium text-secondary-700">错误率</span>
                  <span className="text-lg font-semibold text-error-600">0.8%</span>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-secondary-900">热门数据源</h3>
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-primary-500 rounded-full mr-3"></div>
                    <span className="text-sm text-secondary-900">HIS系统</span>
                  </div>
                  <span className="text-sm font-medium text-secondary-600">45%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-success-500 rounded-full mr-3"></div>
                    <span className="text-sm text-secondary-900">LIS系统</span>
                  </div>
                  <span className="text-sm font-medium text-secondary-600">28%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-warning-500 rounded-full mr-3"></div>
                    <span className="text-sm text-secondary-900">PACS系统</span>
                  </div>
                  <span className="text-sm font-medium text-secondary-600">18%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-error-500 rounded-full mr-3"></div>
                    <span className="text-sm text-secondary-900">药房系统</span>
                  </div>
                  <span className="text-sm font-medium text-secondary-600">9%</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
