'use client';

import { useState } from 'react';
import Link from 'next/link';

interface SystemFormData {
  // 单位信息
  organizationName: string;
  organizationCode: string;
  organizationType: string;
  address: string;

  // 责任人信息
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  contactPosition: string;

  // 业务属性
  systemName: string;
  systemType: string;
  systemVersion: string;
  businessScope: string;
  dataTypes: string[];

  // 技术信息
  techStack: string;
  databaseType: string;
  apiEndpoint: string;
  authMethod: string;
}

export default function SystemRegister() {
  const [formData, setFormData] = useState<SystemFormData>({
    organizationName: '',
    organizationCode: '',
    organizationType: '医院',
    address: '',
    contactName: '',
    contactPhone: '',
    contactEmail: '',
    contactPosition: '',
    systemName: '',
    systemType: 'HIS',
    systemVersion: '',
    businessScope: '',
    dataTypes: [],
    techStack: '',
    databaseType: 'MySQL',
    apiEndpoint: '',
    authMethod: 'API_KEY'
  });

  const [currentStep, setCurrentStep] = useState(1);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDataTypeChange = (dataType: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      dataTypes: checked
        ? [...prev.dataTypes, dataType]
        : prev.dataTypes.filter(type => type !== dataType)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('提交系统注册信息:', formData);
    // 这里会调用后端API
    alert('系统注册申请已提交，请等待审核！');
  };

  const steps = [
    { id: 1, name: '单位信息', description: '填写医院基本信息' },
    { id: 2, name: '责任人信息', description: '填写系统负责人信息' },
    { id: 3, name: '业务属性', description: '配置系统业务信息' },
    { id: 4, name: '技术配置', description: '设置技术参数' }
  ];

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-soft border-b border-secondary-200">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-primary-600">YY DataX</Link>
              <span className="ml-4 text-secondary-500">/</span>
              <span className="ml-4 text-secondary-700">业务系统注册</span>
            </div>
            <Link href="/" className="text-secondary-600 hover:text-secondary-900">
              返回首页
            </Link>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-900">业务系统注册</h1>
          <p className="mt-2 text-lg text-secondary-600">
            注册您的医院业务系统，开始数据交换服务
          </p>
        </div>

        {/* 步骤指示器 */}
        <div className="mb-8">
          <nav aria-label="Progress">
            <ol className="flex items-center">
              {steps.map((step, stepIdx) => (
                <li key={step.id} className={`${stepIdx !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''} relative`}>
                  <div className="flex items-center">
                    <div className={`
                      flex h-10 w-10 items-center justify-center rounded-full border-2
                      ${currentStep >= step.id
                        ? 'border-primary-600 bg-primary-600 text-white'
                        : 'border-secondary-300 bg-white text-secondary-500'
                      }
                    `}>
                      {currentStep > step.id ? (
                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        <span className="text-sm font-medium">{step.id}</span>
                      )}
                    </div>
                    <div className="ml-4 min-w-0">
                      <p className={`text-sm font-medium ${currentStep >= step.id ? 'text-primary-600' : 'text-secondary-500'}`}>
                        {step.name}
                      </p>
                      <p className="text-sm text-secondary-500">{step.description}</p>
                    </div>
                  </div>
                  {stepIdx !== steps.length - 1 && (
                    <div className="absolute top-5 right-0 hidden h-0.5 w-full bg-secondary-200 sm:block" aria-hidden="true">
                      <div className={`h-0.5 ${currentStep > step.id ? 'bg-primary-600' : 'bg-secondary-200'}`} />
                    </div>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="card">
          {/* 第一步：单位信息 */}
          {currentStep === 1 && (
            <div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-6">单位信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="label">医院名称 *</label>
                  <input
                    type="text"
                    name="organizationName"
                    value={formData.organizationName}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="如：北京协和医院"
                    required
                  />
                </div>
                <div>
                  <label className="label">医院代码 *</label>
                  <input
                    type="text"
                    name="organizationCode"
                    value={formData.organizationCode}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="如：110101001"
                    required
                  />
                </div>
                <div>
                  <label className="label">医院类型 *</label>
                  <select
                    name="organizationType"
                    value={formData.organizationType}
                    onChange={handleInputChange}
                    className="input-field"
                    required
                  >
                    <option value="医院">医院</option>
                    <option value="三甲医院">三甲医院</option>
                    <option value="二甲医院">二甲医院</option>
                    <option value="专科医院">专科医院</option>
                    <option value="社区医院">社区医院</option>
                  </select>
                </div>
                <div>
                  <label className="label">医院地址 *</label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="详细地址"
                    required
                  />
                </div>
              </div>
            </div>
          )}

          {/* 第二步：责任人信息 */}
          {currentStep === 2 && (
            <div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-6">责任人信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="label">联系人姓名 *</label>
                  <input
                    type="text"
                    name="contactName"
                    value={formData.contactName}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="系统负责人姓名"
                    required
                  />
                </div>
                <div>
                  <label className="label">联系电话 *</label>
                  <input
                    type="tel"
                    name="contactPhone"
                    value={formData.contactPhone}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="手机号码"
                    required
                  />
                </div>
                <div>
                  <label className="label">邮箱地址 *</label>
                  <input
                    type="email"
                    name="contactEmail"
                    value={formData.contactEmail}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="邮箱地址"
                    required
                  />
                </div>
                <div>
                  <label className="label">职务 *</label>
                  <input
                    type="text"
                    name="contactPosition"
                    value={formData.contactPosition}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="如：信息科主任"
                    required
                  />
                </div>
              </div>
            </div>
          )}

          {/* 第三步：业务属性 */}
          {currentStep === 3 && (
            <div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-6">业务属性</h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="label">系统名称 *</label>
                    <input
                      type="text"
                      name="systemName"
                      value={formData.systemName}
                      onChange={handleInputChange}
                      className="input-field"
                      placeholder="如：医院信息系统"
                      required
                    />
                  </div>
                  <div>
                    <label className="label">系统类型 *</label>
                    <select
                      name="systemType"
                      value={formData.systemType}
                      onChange={handleInputChange}
                      className="input-field"
                      required
                    >
                      <option value="HIS">HIS - 医院信息系统</option>
                      <option value="LIS">LIS - 检验信息系统</option>
                      <option value="PACS">PACS - 影像存档系统</option>
                      <option value="RIS">RIS - 放射信息系统</option>
                      <option value="EMR">EMR - 电子病历系统</option>
                      <option value="CIS">CIS - 临床信息系统</option>
                      <option value="PHARMACY">药房管理系统</option>
                      <option value="OTHER">其他</option>
                    </select>
                  </div>
                  <div>
                    <label className="label">系统版本</label>
                    <input
                      type="text"
                      name="systemVersion"
                      value={formData.systemVersion}
                      onChange={handleInputChange}
                      className="input-field"
                      placeholder="如：v2.1.0"
                    />
                  </div>
                </div>

                <div>
                  <label className="label">业务范围描述</label>
                  <textarea
                    name="businessScope"
                    value={formData.businessScope}
                    onChange={handleInputChange}
                    className="input-field"
                    rows={3}
                    placeholder="描述系统主要业务功能和覆盖范围"
                  />
                </div>

                <div>
                  <label className="label">数据类型 *</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                    {[
                      '患者基本信息', '门诊记录', '住院记录', '检验报告',
                      '影像资料', '手术记录', '药品信息', '费用信息',
                      '医嘱信息', '护理记录', '病历文档', '其他'
                    ].map((dataType) => (
                      <label key={dataType} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.dataTypes.includes(dataType)}
                          onChange={(e) => handleDataTypeChange(dataType, e.target.checked)}
                          className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-secondary-700">{dataType}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 第四步：技术配置 */}
          {currentStep === 4 && (
            <div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-6">技术配置</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="label">技术架构</label>
                  <input
                    type="text"
                    name="techStack"
                    value={formData.techStack}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="如：Java Spring Boot"
                  />
                </div>
                <div>
                  <label className="label">数据库类型 *</label>
                  <select
                    name="databaseType"
                    value={formData.databaseType}
                    onChange={handleInputChange}
                    className="input-field"
                    required
                  >
                    <option value="MySQL">MySQL</option>
                    <option value="PostgreSQL">PostgreSQL</option>
                    <option value="Oracle">Oracle</option>
                    <option value="SQL Server">SQL Server</option>
                    <option value="MongoDB">MongoDB</option>
                    <option value="Other">其他</option>
                  </select>
                </div>
                <div>
                  <label className="label">API接口地址</label>
                  <input
                    type="url"
                    name="apiEndpoint"
                    value={formData.apiEndpoint}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="https://api.hospital.com"
                  />
                </div>
                <div>
                  <label className="label">认证方式 *</label>
                  <select
                    name="authMethod"
                    value={formData.authMethod}
                    onChange={handleInputChange}
                    className="input-field"
                    required
                  >
                    <option value="API_KEY">API Key</option>
                    <option value="OAUTH2">OAuth 2.0</option>
                    <option value="JWT">JWT Token</option>
                    <option value="BASIC">Basic Auth</option>
                    <option value="CUSTOM">自定义</option>
                  </select>
                </div>
              </div>

              <div className="mt-6 p-4 bg-warning-50 border border-warning-200 rounded-lg">
                <div className="flex">
                  <svg className="w-5 h-5 text-warning-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <div className="ml-3">
                    <h4 className="text-sm font-medium text-warning-800">注意事项</h4>
                    <p className="mt-1 text-sm text-warning-700">
                      提交注册申请后，我们的技术团队将在1-2个工作日内联系您进行技术对接和测试。
                      请确保提供的技术信息准确无误。
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 导航按钮 */}
          <div className="flex justify-between mt-8 pt-6 border-t border-secondary-200">
            <button
              type="button"
              onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
              disabled={currentStep === 1}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一步
            </button>

            {currentStep < 4 ? (
              <button
                type="button"
                onClick={() => setCurrentStep(Math.min(4, currentStep + 1))}
                className="btn-primary"
              >
                下一步
              </button>
            ) : (
              <button
                type="submit"
                className="btn-primary"
              >
                提交注册
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}
