{"$schema": "./node_modules/nx/schemas/nx-schema.json", "defaultBase": "master", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/src/test-setup.[jt]s"], "sharedGlobals": []}, "plugins": [{"plugin": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}, {"plugin": "@nx/react/router-plugin", "options": {"buildTargetName": "build", "devTargetName": "dev", "startTargetName": "start", "watchDepsTargetName": "watch-deps", "buildDepsTargetName": "build-deps", "typecheckTargetName": "typecheck"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/next/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "devTargetName": "dev", "serveStaticTargetName": "serve-static", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}], "generators": {"@nx/react": {"application": {"babel": true, "style": "css", "linter": "eslint", "bundler": "vite"}, "component": {"style": "css"}, "library": {"style": "css", "linter": "eslint"}}, "@nx/next": {"application": {"style": "css", "linter": "eslint"}}}}