{"name": "@yy/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "dependencies": {"next": "~15.2.4", "react": "19.0.0", "react-dom": "19.0.0", "react-router-dom": "6.29.0"}, "devDependencies": {"@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^2.1.1", "@eslint/js": "^9.8.0", "@next/eslint-plugin-next": "^15.2.4", "@nx/eslint": "21.5.3", "@nx/eslint-plugin": "21.5.3", "@nx/js": "21.5.3", "@nx/next": "^21.5.3", "@nx/react": "21.5.3", "@nx/vite": "21.5.3", "@nx/web": "21.5.3", "@nx/workspace": "21.5.3", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.18", "@types/node": "^20.0.0", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/ui": "^3.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.8.0", "eslint-config-next": "^15.2.4", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jiti": "2.4.2", "jsdom": "~22.1.0", "nx": "21.5.3", "postcss": "^8.5.6", "prettier": "^2.6.2", "tailwindcss": "^3.4.17", "tslib": "^2.3.0", "typescript": "~5.9.2", "typescript-eslint": "^8.40.0", "vite": "^7.0.0", "vitest": "^3.0.0"}}