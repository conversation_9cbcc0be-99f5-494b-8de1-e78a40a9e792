'use client';

import { useState } from 'react';
import Link from 'next/link';

interface DataResource {
  id: string;
  name: string;
  description: string;
  provider: string;
  category: string;
  dataType: string;
  updateFrequency: string;
  accessMethod: string[];
  status: 'available' | 'restricted' | 'maintenance';
  tags: string[];
  lastUpdated: string;
  subscribers: number;
}

const mockDataResources: DataResource[] = [
  {
    id: '1',
    name: '患者基本信息',
    description: '包含患者姓名、性别、年龄、身份证号、联系方式等基本信息',
    provider: 'HIS系统',
    category: '患者信息',
    dataType: '结构化数据',
    updateFrequency: '实时',
    accessMethod: ['HTTP API', '数据库同步'],
    status: 'available',
    tags: ['患者', '基本信息', '实时'],
    lastUpdated: '2024-01-15 14:30:00',
    subscribers: 5
  },
  {
    id: '2',
    name: '检验报告数据',
    description: '血常规、生化、免疫等各类检验项目的报告结果',
    provider: '检验科LIS',
    category: '检验数据',
    dataType: '结构化数据',
    updateFrequency: '每小时',
    accessMethod: ['HTTP API', 'FTP推送'],
    status: 'available',
    tags: ['检验', '报告', '医技'],
    lastUpdated: '2024-01-15 13:45:00',
    subscribers: 3
  },
  {
    id: '3',
    name: '影像检查资料',
    description: 'CT、MRI、X光等影像检查的DICOM文件和报告',
    provider: '影像科PACS',
    category: '影像数据',
    dataType: '文件数据',
    updateFrequency: '每日',
    accessMethod: ['FTP拉取', 'HTTP下载'],
    status: 'restricted',
    tags: ['影像', 'DICOM', '医技'],
    lastUpdated: '2024-01-15 09:20:00',
    subscribers: 2
  },
  {
    id: '4',
    name: '药品库存信息',
    description: '医院药房的药品库存、价格、有效期等信息',
    provider: '药房系统',
    category: '药品信息',
    dataType: '结构化数据',
    updateFrequency: '每4小时',
    accessMethod: ['Kafka消息', 'HTTP API'],
    status: 'maintenance',
    tags: ['药品', '库存', '价格'],
    lastUpdated: '2024-01-14 18:00:00',
    subscribers: 4
  },
  {
    id: '5',
    name: '门诊挂号记录',
    description: '患者门诊挂号的科室、医生、时间等信息',
    provider: 'HIS系统',
    category: '门诊数据',
    dataType: '结构化数据',
    updateFrequency: '实时',
    accessMethod: ['HTTP API', 'MQ推送'],
    status: 'available',
    tags: ['门诊', '挂号', '实时'],
    lastUpdated: '2024-01-15 15:10:00',
    subscribers: 6
  },
  {
    id: '6',
    name: '住院病历文档',
    description: '住院患者的病历记录、医嘱、护理记录等文档',
    provider: 'EMR系统',
    category: '病历数据',
    dataType: '文档数据',
    updateFrequency: '每日',
    accessMethod: ['HTTP API', '数据库同步'],
    status: 'available',
    tags: ['住院', '病历', '文档'],
    lastUpdated: '2024-01-15 12:00:00',
    subscribers: 3
  }
];

export default function DataMap() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('全部');
  const [selectedStatus, setSelectedStatus] = useState('全部');
  const [filteredResources, setFilteredResources] = useState(mockDataResources);

  const categories = ['全部', '患者信息', '检验数据', '影像数据', '药品信息', '门诊数据', '病历数据'];
  const statuses = ['全部', 'available', 'restricted', 'maintenance'];

  const statusLabels = {
    available: '可用',
    restricted: '受限',
    maintenance: '维护中'
  };

  const statusColors = {
    available: 'status-success',
    restricted: 'status-warning',
    maintenance: 'status-error'
  };

  // 过滤数据资源
  const filterResources = () => {
    let filtered = mockDataResources;

    if (searchTerm) {
      filtered = filtered.filter(resource =>
        resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (selectedCategory !== '全部') {
      filtered = filtered.filter(resource => resource.category === selectedCategory);
    }

    if (selectedStatus !== '全部') {
      filtered = filtered.filter(resource => resource.status === selectedStatus);
    }

    setFilteredResources(filtered);
  };

  // 当搜索条件改变时重新过滤
  useState(() => {
    filterResources();
  });

  const handleSubscribe = (resourceId: string) => {
    alert(`申请订阅数据资源: ${resourceId}`);
    // 这里会跳转到订阅申请页面
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-soft border-b border-secondary-200">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-primary-600">YY DataX</Link>
              <span className="ml-4 text-secondary-500">/</span>
              <span className="ml-4 text-secondary-700">数据地图</span>
            </div>
            <Link href="/" className="text-secondary-600 hover:text-secondary-900">
              返回首页
            </Link>
          </div>
        </div>
      </nav>

      <div className="max-w-full mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-900">数据地图门户</h1>
          <p className="mt-2 text-lg text-secondary-600">
            浏览和订阅医院各业务系统的数据资源
          </p>
        </div>

        {/* 搜索和过滤区域 */}
        <div className="card mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-2">
              <label className="label">搜索数据资源</label>
              <div className="relative">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    filterResources();
                  }}
                  className="input-field pl-10"
                  placeholder="搜索数据名称、描述或标签..."
                />
                <svg className="absolute left-3 top-3 h-4 w-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            <div>
              <label className="label">数据分类</label>
              <select
                value={selectedCategory}
                onChange={(e) => {
                  setSelectedCategory(e.target.value);
                  filterResources();
                }}
                className="input-field"
              >
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="label">状态</label>
              <select
                value={selectedStatus}
                onChange={(e) => {
                  setSelectedStatus(e.target.value);
                  filterResources();
                }}
                className="input-field"
              >
                {statuses.map(status => (
                  <option key={status} value={status}>
                    {status === '全部' ? '全部' : statusLabels[status as keyof typeof statusLabels]}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="card text-center">
            <div className="text-2xl font-bold text-primary-600">{mockDataResources.length}</div>
            <div className="text-sm text-secondary-600">总数据资源</div>
          </div>
          <div className="card text-center">
            <div className="text-2xl font-bold text-success-600">
              {mockDataResources.filter(r => r.status === 'available').length}
            </div>
            <div className="text-sm text-secondary-600">可用资源</div>
          </div>
          <div className="card text-center">
            <div className="text-2xl font-bold text-warning-600">
              {mockDataResources.filter(r => r.status === 'restricted').length}
            </div>
            <div className="text-sm text-secondary-600">受限资源</div>
          </div>
          <div className="card text-center">
            <div className="text-2xl font-bold text-secondary-600">
              {mockDataResources.reduce((sum, r) => sum + r.subscribers, 0)}
            </div>
            <div className="text-sm text-secondary-600">总订阅数</div>
          </div>
        </div>

        {/* 数据资源列表 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredResources.map((resource) => (
            <div key={resource.id} className="card hover:shadow-medium transition-shadow duration-200">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">{resource.name}</h3>
                  <span className={`status-badge ${statusColors[resource.status]}`}>
                    {statusLabels[resource.status]}
                  </span>
                </div>
                <button
                  onClick={() => handleSubscribe(resource.id)}
                  disabled={resource.status === 'maintenance'}
                  className="btn-primary text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  订阅
                </button>
              </div>

              <p className="text-secondary-600 mb-4">{resource.description}</p>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-secondary-500">提供方:</span>
                  <span className="text-secondary-900">{resource.provider}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-500">数据类型:</span>
                  <span className="text-secondary-900">{resource.dataType}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-500">更新频率:</span>
                  <span className="text-secondary-900">{resource.updateFrequency}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-500">订阅数:</span>
                  <span className="text-secondary-900">{resource.subscribers}</span>
                </div>
              </div>

              <div className="mt-4">
                <div className="text-sm text-secondary-500 mb-2">访问方式:</div>
                <div className="flex flex-wrap gap-2">
                  {resource.accessMethod.map((method) => (
                    <span key={method} className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs">
                      {method}
                    </span>
                  ))}
                </div>
              </div>

              <div className="mt-4">
                <div className="text-sm text-secondary-500 mb-2">标签:</div>
                <div className="flex flex-wrap gap-2">
                  {resource.tags.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-secondary-200">
                <div className="text-xs text-secondary-500">
                  最后更新: {resource.lastUpdated}
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredResources.length === 0 && (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-secondary-900">没有找到数据资源</h3>
            <p className="mt-1 text-sm text-secondary-500">请尝试调整搜索条件</p>
          </div>
        )}
      </div>
    </div>
  );
}
