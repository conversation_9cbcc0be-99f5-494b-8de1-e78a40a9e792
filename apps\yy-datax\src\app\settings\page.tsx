'use client';

import { useState } from 'react';
import Link from 'next/link';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'operator' | 'viewer';
  status: 'active' | 'inactive';
  lastLogin: string;
}

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<'system' | 'users' | 'security' | 'notifications'>('system');

  // 示例用户数据
  const users: User[] = [
    {
      id: '1',
      name: '张三',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      lastLogin: '2小时前'
    },
    {
      id: '2',
      name: '李四',
      email: '<EMAIL>',
      role: 'operator',
      status: 'active',
      lastLogin: '1天前'
    },
    {
      id: '3',
      name: '王五',
      email: '<EMAIL>',
      role: 'viewer',
      status: 'inactive',
      lastLogin: '1周前'
    }
  ];

  const getRoleBadge = (role: string) => {
    const roleMap = {
      admin: 'status-error',
      operator: 'status-warning',
      viewer: 'status-success'
    };
    return roleMap[role as keyof typeof roleMap] || 'status-secondary';
  };

  const getRoleText = (role: string) => {
    const roleMap = {
      admin: '管理员',
      operator: '操作员',
      viewer: '查看者'
    };
    return roleMap[role as keyof typeof roleMap] || role;
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: 'status-success',
      inactive: 'status-secondary'
    };
    return statusMap[status as keyof typeof statusMap] || 'status-secondary';
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      active: '活跃',
      inactive: '停用'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100">
      {/* 导航栏 */}
      <nav className="bg-white shadow-soft border-b border-secondary-200">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Link href="/">
                  <h1 className="text-2xl font-bold text-primary-600">YY DataX</h1>
                </Link>
              </div>
              <div className="hidden md:block ml-10">
                <div className="flex items-baseline space-x-4">
                  <Link href="/" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    首页
                  </Link>
                  <Link href="/data-map" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    数据地图
                  </Link>
                  <Link href="/system-register" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    系统注册
                  </Link>
                  <Link href="/data-subscription" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    数据订阅
                  </Link>
                  <Link href="/data-integration" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    数据对接
                  </Link>
                  <Link href="/monitoring" className="text-secondary-600 hover:text-secondary-900 px-3 py-2 rounded-md text-sm font-medium">
                    监控
                  </Link>
                  <Link href="/settings" className="text-primary-600 hover:text-primary-800 px-3 py-2 rounded-md text-sm font-medium">
                    设置
                  </Link>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-secondary-600">欢迎，管理员</span>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-full mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-900 mb-2">系统设置</h1>
          <p className="text-lg text-secondary-600">管理系统配置、用户权限和安全设置</p>
        </div>

        {/* 标签页导航 */}
        <div className="mb-6">
          <div className="border-b border-secondary-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('system')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'system'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                系统配置
              </button>
              <button
                onClick={() => setActiveTab('users')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'users'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                用户管理
              </button>
              <button
                onClick={() => setActiveTab('security')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'security'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                安全设置
              </button>
              <button
                onClick={() => setActiveTab('notifications')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'notifications'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                通知设置
              </button>
            </nav>
          </div>
        </div>

        {/* 系统配置 */}
        {activeTab === 'system' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-secondary-900">基本设置</h3>
              </div>
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">系统名称</label>
                  <input type="text" className="input-field" defaultValue="YY DataX 医院数据交换平台" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">系统描述</label>
                  <textarea className="input-field" rows={3} defaultValue="安全、高效的医疗数据对接与共享服务平台"></textarea>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">管理员邮箱</label>
                  <input type="email" className="input-field" defaultValue="<EMAIL>" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">时区设置</label>
                  <select className="input-field">
                    <option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</option>
                    <option value="UTC">UTC (UTC+0)</option>
                  </select>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" id="maintenance" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" />
                  <label htmlFor="maintenance" className="ml-2 block text-sm text-secondary-900">
                    启用维护模式
                  </label>
                </div>
                <button type="submit" className="btn-primary">保存设置</button>
              </form>
            </div>

            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-secondary-900">性能配置</h3>
              </div>
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">最大并发连接数</label>
                  <input type="number" className="input-field" defaultValue="100" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">连接超时时间（秒）</label>
                  <input type="number" className="input-field" defaultValue="30" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">批处理大小</label>
                  <input type="number" className="input-field" defaultValue="1000" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">日志保留天数</label>
                  <input type="number" className="input-field" defaultValue="30" />
                </div>
                <div className="flex items-center">
                  <input type="checkbox" id="autoCleanup" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" defaultChecked />
                  <label htmlFor="autoCleanup" className="ml-2 block text-sm text-secondary-900">
                    自动清理过期日志
                  </label>
                </div>
                <button type="submit" className="btn-primary">保存设置</button>
              </form>
            </div>
          </div>
        )}

        {/* 用户管理 */}
        {activeTab === 'users' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-secondary-900">用户列表</h3>
              <button className="btn-primary">添加用户</button>
            </div>

            <div className="card">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-secondary-200">
                  <thead className="table-header">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        用户信息
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        角色
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        最后登录
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-secondary-200">
                    {users.map((user) => (
                      <tr key={user.id} className="table-row">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                              <span className="text-sm font-medium text-primary-600">
                                {user.name.charAt(0)}
                              </span>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-secondary-900">{user.name}</div>
                              <div className="text-sm text-secondary-500">{user.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`status-badge ${getRoleBadge(user.role)}`}>
                            {getRoleText(user.role)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`status-badge ${getStatusBadge(user.status)}`}>
                            {getStatusText(user.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                          {user.lastLogin}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button className="text-primary-600 hover:text-primary-900 mr-3">编辑</button>
                          <button className="text-warning-600 hover:text-warning-900 mr-3">
                            {user.status === 'active' ? '停用' : '启用'}
                          </button>
                          <button className="text-error-600 hover:text-error-900">删除</button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="card max-w-2xl">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-secondary-900">添加新用户</h3>
              </div>
              <form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">姓名</label>
                    <input type="text" className="input-field" placeholder="请输入姓名" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">邮箱</label>
                    <input type="email" className="input-field" placeholder="请输入邮箱地址" />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">角色</label>
                  <select className="input-field">
                    <option value="">请选择角色</option>
                    <option value="admin">管理员</option>
                    <option value="operator">操作员</option>
                    <option value="viewer">查看者</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">初始密码</label>
                  <input type="password" className="input-field" placeholder="请输入初始密码" />
                </div>
                <div className="flex items-center">
                  <input type="checkbox" id="sendEmail" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" />
                  <label htmlFor="sendEmail" className="ml-2 block text-sm text-secondary-900">
                    发送邮件通知用户
                  </label>
                </div>
                <div className="flex space-x-3">
                  <button type="button" className="btn-secondary">取消</button>
                  <button type="submit" className="btn-primary">添加用户</button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* 安全设置 */}
        {activeTab === 'security' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-secondary-900">密码策略</h3>
              </div>
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">最小密码长度</label>
                  <input type="number" className="input-field" defaultValue="8" min="6" max="20" />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-secondary-700">密码复杂度要求</label>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input type="checkbox" id="requireUppercase" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" defaultChecked />
                      <label htmlFor="requireUppercase" className="ml-2 block text-sm text-secondary-900">
                        包含大写字母
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" id="requireLowercase" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" defaultChecked />
                      <label htmlFor="requireLowercase" className="ml-2 block text-sm text-secondary-900">
                        包含小写字母
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" id="requireNumbers" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" defaultChecked />
                      <label htmlFor="requireNumbers" className="ml-2 block text-sm text-secondary-900">
                        包含数字
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" id="requireSpecial" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" />
                      <label htmlFor="requireSpecial" className="ml-2 block text-sm text-secondary-900">
                        包含特殊字符
                      </label>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">密码有效期（天）</label>
                  <input type="number" className="input-field" defaultValue="90" />
                </div>
                <button type="submit" className="btn-primary">保存设置</button>
              </form>
            </div>

            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-secondary-900">访问控制</h3>
              </div>
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">会话超时时间（分钟）</label>
                  <input type="number" className="input-field" defaultValue="30" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">最大登录失败次数</label>
                  <input type="number" className="input-field" defaultValue="5" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">账户锁定时间（分钟）</label>
                  <input type="number" className="input-field" defaultValue="15" />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-secondary-700">IP白名单</label>
                  <textarea className="input-field" rows={4} placeholder="每行一个IP地址或IP段，例如：&#10;***********/24&#10;********"></textarea>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" id="enableTwoFactor" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" />
                  <label htmlFor="enableTwoFactor" className="ml-2 block text-sm text-secondary-900">
                    启用双因素认证
                  </label>
                </div>
                <button type="submit" className="btn-primary">保存设置</button>
              </form>
            </div>
          </div>
        )}

        {/* 通知设置 */}
        {activeTab === 'notifications' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-secondary-900">邮件通知</h3>
              </div>
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">SMTP服务器</label>
                  <input type="text" className="input-field" placeholder="smtp.example.com" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">端口</label>
                    <input type="number" className="input-field" defaultValue="587" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">加密方式</label>
                    <select className="input-field">
                      <option value="tls">TLS</option>
                      <option value="ssl">SSL</option>
                      <option value="none">无</option>
                    </select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">用户名</label>
                    <input type="text" className="input-field" placeholder="username" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">密码</label>
                    <input type="password" className="input-field" placeholder="password" />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">发件人地址</label>
                  <input type="email" className="input-field" placeholder="<EMAIL>" />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-secondary-700">通知类型</label>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input type="checkbox" id="notifyError" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" defaultChecked />
                      <label htmlFor="notifyError" className="ml-2 block text-sm text-secondary-900">
                        系统错误通知
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" id="notifyWarning" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" defaultChecked />
                      <label htmlFor="notifyWarning" className="ml-2 block text-sm text-secondary-900">
                        警告通知
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" id="notifyMaintenance" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" />
                      <label htmlFor="notifyMaintenance" className="ml-2 block text-sm text-secondary-900">
                        维护通知
                      </label>
                    </div>
                  </div>
                </div>
                <div className="flex space-x-3">
                  <button type="button" className="btn-secondary">测试连接</button>
                  <button type="submit" className="btn-primary">保存设置</button>
                </div>
              </form>
            </div>

            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-secondary-900">告警规则</h3>
              </div>
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">CPU使用率阈值（%）</label>
                  <input type="number" className="input-field" defaultValue="80" min="0" max="100" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">内存使用率阈值（%）</label>
                  <input type="number" className="input-field" defaultValue="85" min="0" max="100" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">磁盘使用率阈值（%）</label>
                  <input type="number" className="input-field" defaultValue="90" min="0" max="100" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">连续失败次数阈值</label>
                  <input type="number" className="input-field" defaultValue="3" min="1" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">响应时间阈值（秒）</label>
                  <input type="number" className="input-field" defaultValue="10" min="1" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">告警接收人邮箱</label>
                  <textarea className="input-field" rows={3} placeholder="每行一个邮箱地址"></textarea>
                </div>
                <button type="submit" className="btn-primary">保存设置</button>
              </form>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
